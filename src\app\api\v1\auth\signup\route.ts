import { NextRequest, NextResponse } from 'next/server'
import { signUpSchema } from '@/lib/validations/auth'
import type { APIResponse } from '@/lib/types'
import { getDatabaseService } from '@/lib/database/service'
import { z } from 'zod'

// Rate limiting (in production, use Redis or similar)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

function rateLimit(ip: string, limit: number = 5, windowMs: number = 15 * 60 * 1000): boolean {
	const now = Date.now()
	const record = rateLimitMap.get(ip)

	if (!record || now > record.resetTime) {
		rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs })
		return true
	}

	if (record.count >= limit) {
		return false
	}

	record.count++
	return true
}

export async function POST(request: NextRequest): Promise<NextResponse<APIResponse>> {
	const db = getDatabaseService()

	try {
		// Ensure database connection
		await db.connect()

		// Rate limiting
		const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
		if (!rateLimit(ip)) {
			return NextResponse.json({ error: 'Too many requests. Please try again later.' }, { status: 429 })
		}

		// Parse and validate request body
		const body = await request.json()
		const validatedData = signUpSchema.parse(body)

		// Use database transaction for atomicity
		const result = await db.transaction(async () => {
			// Check if user already exists
			const existingUser = await db.getUserByEmail(validatedData.email)
			if (existingUser) {
				throw new Error('An account with this email already exists. Please sign in instead.')
			}

			// Create organization if needed
			let organizationId: string | undefined
			if (validatedData.organizationName && ['broker', 'developer'].includes(validatedData.role)) {
				const organization = await db.createOrganization({
					name: validatedData.organizationName,
					plan: 'free',
					settings: {
						allowPublicListings: true,
						requireApproval: false,
						defaultLanguage: 'en',
						timezone: 'UTC'
					}
				})
				organizationId = organization.id
			}

			// Create user with hashed password
			const user = await db.createUser({
				email: validatedData.email,
				name: validatedData.name || '',
				passwordHash: await db.hashPassword(validatedData.password),
				role: validatedData.role,
				organizationId
			})

			// Create session for immediate login
			const session = await db.createSession(user.id)

			return { user, session }
		})

		// Return success response (exclude sensitive data)
		const response: APIResponse = {
			data: {
				user: {
					id: result.user.id,
					email: result.user.email,
					name: result.user.name,
					role: result.user.role,
					organizationId: result.user.organizationId,
					createdAt: result.user.createdAt
				}
			},
			message: 'Account created successfully! Welcome to RealHub.'
		}

		// Set secure session cookie
		const nextResponse = NextResponse.json(response, { status: 201 })
		nextResponse.cookies.set('session', result.session.token, {
			httpOnly: true,
			secure: process.env.NODE_ENV === 'production',
			sameSite: 'lax',
			maxAge: 30 * 24 * 60 * 60, // 30 days
			path: '/'
		})

		return nextResponse
	} catch (error) {
		console.error('API Signup error:', error)

		if (error instanceof Error && error.message.includes('already exists')) {
			return NextResponse.json({ error: error.message }, { status: 409 })
		}

		if (error instanceof z.ZodError) {
			const firstError = error.errors[0]
			return NextResponse.json({ error: firstError?.message || 'Invalid input data.' }, { status: 400 })
		}

		if (error instanceof SyntaxError) {
			return NextResponse.json({ error: 'Invalid JSON format.' }, { status: 400 })
		}

		return NextResponse.json({ error: 'Internal server error. Please try again later.' }, { status: 500 })
	}
}

// Handle unsupported methods
export async function GET(): Promise<NextResponse> {
	return NextResponse.json({ error: 'Method not allowed. Use POST to create an account.' }, { status: 405 })
}

export async function PUT(): Promise<NextResponse> {
	return NextResponse.json({ error: 'Method not allowed. Use POST to create an account.' }, { status: 405 })
}

export async function DELETE(): Promise<NextResponse> {
	return NextResponse.json({ error: 'Method not allowed. Use POST to create an account.' }, { status: 405 })
}

export async function PATCH(): Promise<NextResponse> {
	return NextResponse.json({ error: 'Method not allowed. Use POST to create an account.' }, { status: 405 })
}
